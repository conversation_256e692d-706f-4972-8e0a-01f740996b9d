{"name": "mask_vue3_element", "type": "module", "version": "2.0.3", "description": "🎉 基于 Element + Vue3 的管理系统", "author": "TsMask", "repository": {"type": "git", "url": "https://gitee.com/TsMask/mask_vue3_element"}, "license": "MIT", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueuse/core": "12.7.0", "axios": "1.8.0", "echarts": "5.6.0", "element-plus": "2.9.5", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-base64": "3.7.7", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "2.3.1", "vue": "3.5.13", "vue-cropper": "1.1.4", "vue-router": "4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.1", "@vue/compiler-sfc": "3.5.13", "sass": "1.65.0", "unplugin-auto-import": "0.19.0", "unplugin-vue-components": "0.28.0", "vite": "6.2.0", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.2"}}
<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :lg="8" :md="8" :xs="24">
        <el-card style="height: calc(100vh - 125px)">
          <template #header>
            <Collection
              style="width: 1em; height: 1em; vertical-align: middle"
            />
            <span style="vertical-align: middle">缓存列表</span>
            <div style="float: right; padding: 3px 0">
              <el-button
                link
                type="primary"
                icon="Refresh"
                @click="refreshCacheNames()"
              >
                刷新
              </el-button>
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleClearCacheAll()"
              >
                安全清理
              </el-button>
            </div>
          </template>
          <el-table
            v-loading="loading"
            :data="cacheNames"
            :height="tableHeight"
            highlight-current-row
            @row-click="getCacheKeys"
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              width="60"
              type="index"
            ></el-table-column>

            <el-table-column
              label="缓存名称"
              align="left"
              prop="cacheName"
              :show-overflow-tooltip="true"
            ></el-table-column>

            <el-table-column
              label="备注"
              align="left"
              prop="remark"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="操作"
              width="60"
              align="left"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Delete"
                  @click="handleClearCacheName(scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :xs="24">
        <el-card style="height: calc(100vh - 125px)">
          <template #header>
            <Key style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">键名列表</span>
            <el-button
              style="float: right; padding: 3px 0"
              link
              type="primary"
              icon="Refresh"
              @click="refreshCacheKeys()"
            ></el-button>
          </template>
          <el-table
            v-loading="subLoading"
            :data="cacheKeys"
            :height="tableHeight"
            highlight-current-row
            @row-click="handleCacheValue"
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              width="60"
              type="index"
            ></el-table-column>
            <el-table-column
              label="缓存键名"
              align="left"
              prop="cacheKey"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              width="60"
              align="left"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Delete"
                  @click="handleClearCacheKey(scope.row.cacheKey)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :xs="24">
        <el-card :bordered="false" style="height: calc(100vh - 125px)">
          <template #header>
            <Document style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">缓存内容</span>
          </template>
          <el-form :model="cacheForm">
            <el-row :gutter="32">
              <el-col :offset="1" :span="22">
                <el-form-item label="缓存名称:" prop="cacheName">
                  <el-input :value="cacheForm.cacheName" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :offset="1" :span="22">
                <el-form-item label="缓存键名:" prop="cacheKey">
                  <el-input :value="cacheForm.cacheKey" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :offset="1" :span="22">
                <el-form-item label="缓存内容:" prop="cacheValue">
                  <el-input
                    :value="cacheForm.cacheValue"
                    type="textarea"
                    :rows="16"
                    :disabled="true"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {
  listCacheName,
  listCacheKey,
  getCacheValue,
  clearCacheName,
  clearCacheKey,
  clearCacheAll,
} from '@/api/monitor/cache';

const { proxy } = getCurrentInstance();

const cacheNames = ref([]);
const cacheKeys = ref([]);
const cacheForm = ref({});
const loading = ref(true);
const subLoading = ref(false);
const nowCacheName = ref('');
const tableHeight = ref(window.innerHeight - 200);

/** 查询缓存名称列表 */
function getCacheNames() {
  loading.value = true;
  listCacheName().then(response => {
    cacheNames.value = response.data;
    loading.value = false;
  });
}

/** 刷新缓存名称列表 */
function refreshCacheNames() {
  getCacheNames();
  proxy.$modal.msgSuccess('刷新缓存列表成功');
}

/** 清理指定名称缓存 */
function handleClearCacheName(row) {
  clearCacheName(row.cacheName).then(response => {
    proxy.$modal.msgSuccess('清理缓存名称[' + row.cacheName + ']成功');
    getCacheKeys();
    cacheForm.value = {};
  });
}

/** 查询缓存键名列表 */
function getCacheKeys(row = {}) {
  const cacheName = row.cacheName || nowCacheName.value;
  if (!cacheName) {
    return;
  }
  subLoading.value = true;
  listCacheKey(cacheName).then(response => {
    cacheKeys.value = response.data;
    subLoading.value = false;
    nowCacheName.value = cacheName;
  });
}

/** 刷新缓存键名列表 */
function refreshCacheKeys() {
  getCacheKeys();
  proxy.$modal.msgSuccess('刷新键名列表成功');
}

/** 清理指定键名缓存 */
function handleClearCacheKey(cacheKey) {
  clearCacheKey(nowCacheName.value, cacheKey).then(response => {
    proxy.$modal.msgSuccess('清理缓存键名[' + cacheKey + ']成功');
    getCacheKeys();
    cacheForm.value = {};
  });
}

/** 查询缓存内容详细 */
function handleCacheValue(row) {
  getCacheValue(nowCacheName.value, row.cacheKey).then(response => {
    cacheForm.value = response.data;
  });
}

/** 清理全部缓存 */
function handleClearCacheAll() {
  clearCacheAll().then(response => {
    proxy.$modal.msgSuccess('清理全部缓存成功');
  });
}

getCacheNames();
</script>

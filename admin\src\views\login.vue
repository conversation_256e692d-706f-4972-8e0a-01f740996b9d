<template>
  <div class="love-container" :style="{ backgroundImage: `url(${currentBg})` }">
    <!-- 3D情话卡片 -->
    <div class="love-card-3d" @click="flipCard">
      <div class="card-inner" :class="{ flipped: isFlipped }">
        <!-- 正面：情话展示 -->
        <div class="card-front">
          <div class="message-box">
            <transition name="message-fade" mode="out-in">
              <p :key="currentMessage.id" class="message-text">
                {{ currentMessage.text }}
              </p>
            </transition>
            <div class="author-tag">—— 专属你的 {{ nickname }}</div>
          </div>
        </div>

        <!-- 背面：情话控制台 -->
        <div class="card-back">
          <div class="control-panel">
            <el-tabs v-model="activeCategory">
              <el-tab-pane v-for="cat in categories" :key="cat" :label="cat">
                <div class="message-list">
                  <div
                    v-for="msg in filteredMessages"
                    :key="msg.id"
                    :class="{ active: msg.id === currentMessage.id }"
                    @click="selectMessage(msg)"
                  >
                    {{ msg.text.substring(0, 20) }}...
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>

            <div class="action-row">
              <el-button
                type="primary"
                icon="el-icon-star-off"
                @click.stop="toggleFavorite"
              >
                {{ isFavorite ? '取消收藏' : '收藏情话' }}
              </el-button>
              <el-button icon="el-icon-share" @click.stop="shareMessage">
                分享甜蜜
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 互动功能区 -->
    <div class="interaction-area">
      <el-button
        circle
        icon="el-icon-refresh"
        @click="nextMessage"
        class="action-btn"
      />
      <el-button
        circle
        :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"
        @click="toggleMusic"
        class="action-btn"
      />
      <el-button
        circle
        icon="el-icon-picture"
        @click="changeBackground"
        class="action-btn"
      />
    </div>

    <!-- 收藏弹窗 -->
    <el-dialog title="❤️ 你的情话收藏夹 ❤️" v-model="showFavorites">
      <div v-if="favoriteMessages.length > 0">
        <div
          v-for="msg in favoriteMessages"
          :key="msg.id"
          class="favorite-item"
        >
          {{ msg.text }}
          <el-button
            type="danger"
            icon="el-icon-delete"
            circle
            size="small"
            @click="removeFavorite(msg.id)"
          />
        </div>
      </div>
      <div v-else class="empty-tip">
        <el-empty description="还没有收藏的情话哦~" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// 用户数据
const nickname = ref('丹丹宝贝'); // 替换为对方昵称

// 情话数据库（含分类标签）
const messages = ref([
  {
    id: 1,
    text: '你的眼睛是星星，每次眨眼都点亮我的宇宙',
    category: '浪漫',
    favorite: false,
  },
  {
    id: 2,
    text: '我想把余生写成诗，字字都是你的名字',
    category: '诗意',
    favorite: false,
  },
  {
    id: 3,
    text: '你是我的bug，让我日思夜想却舍不得修复',
    category: '程序员',
    favorite: false,
  },
  // 更多情话...（实际开发建议使用API动态获取）
  {
    id: 48,
    text: '我们的爱情像Vue，每次更新都更完美',
    category: '程序员',
    favorite: false,
  },
]);

// 背景图库
const backgrounds = ref([
  'https://example.com/bg1.jpg',
  'https://example.com/bg2.jpg',
  'https://example.com/bg3.jpg',
]);

// 响应式数据
const currentMessage = ref(messages.value[0]);
const isFlipped = ref(false);
const activeCategory = ref('全部');
const isPlaying = ref(false);
const showFavorites = ref(false);
const currentBg = ref(backgrounds.value[0]);
const favoriteMessages = ref([]);

// 分类系统
const categories = computed(() => {
  return ['全部', ...new Set(messages.value.map(m => m.category))];
});

// 过滤情话
const filteredMessages = computed(() => {
  return activeCategory.value === '全部'
    ? messages.value
    : messages.value.filter(m => m.category === activeCategory.value);
});

// 情话收藏功能
const isFavorite = computed(() => {
  return favoriteMessages.value.some(fav => fav.id === currentMessage.value.id);
});

const toggleFavorite = () => {
  if (isFavorite.value) {
    favoriteMessages.value = favoriteMessages.value.filter(
      fav => fav.id !== currentMessage.value.id
    );
    ElMessage.success('已移出收藏夹');
  } else {
    favoriteMessages.value.push({ ...currentMessage.value });
    ElMessage.success('❤️ 情话收藏成功！');
  }
};

// 情话互动方法
const nextMessage = () => {
  const currentIndex = messages.value.findIndex(
    m => m.id === currentMessage.value.id
  );
  const nextIndex = (currentIndex + 1) % messages.value.length;
  currentMessage.value = messages.value[nextIndex];
};

const selectMessage = msg => {
  currentMessage.value = msg;
  isFlipped.value = false; // 自动翻回正面
};

const flipCard = () => {
  isFlipped.value = !isFlipped.value;
};

const changeBackground = () => {
  const currentIndex = backgrounds.value.indexOf(currentBg.value);
  const nextIndex = (currentIndex + 1) % backgrounds.value.length;
  currentBg.value = backgrounds.value[nextIndex];
};

const shareMessage = () => {
  // 实际项目可接入微信/QQ分享SDK
  ElMessage.success(`已将情话分享给${nickname}，请查收甜蜜！`);
};

// 初始化
onMounted(() => {
  // 自动轮播情话
  setInterval(nextMessage, 10000);
});
</script>

<style scoped>
.love-container {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: background-image 1s ease;
}

.love-card-3d {
  perspective: 1500px;
  width: 90%;
  max-width: 600px;
  height: 400px;
  margin: 0 auto;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flipped {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
}

.card-front {
  background: rgba(255, 255, 255, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  cursor: pointer;
}

.card-back {
  background: white;
  transform: rotateY(180deg);
  overflow: hidden;
}

.message-box {
  text-align: center;
  padding: 30px;
}

.message-text {
  font-size: 2.2rem;
  line-height: 1.6;
  color: #d81b60;
  font-weight: 500;
  margin-bottom: 30px;
}

.author-tag {
  font-size: 1.3rem;
  color: #e91e63;
  font-style: italic;
}

.control-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.message-list {
  max-height: 250px;
  overflow-y: auto;
  padding: 10px;
}

.message-list > div {
  padding: 12px;
  margin: 8px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.message-list > div:hover {
  background: #fce4ec;
}

.message-list > div.active {
  border-left-color: #e91e63;
  background: #fce4ec;
  font-weight: bold;
}

.action-row {
  padding: 15px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.interaction-area {
  margin-top: 40px;
  display: flex;
  gap: 20px;
}

.action-btn {
  width: 60px;
  height: 60px;
  font-size: 1.5rem;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.favorite-item {
  padding: 15px;
  margin: 10px 0;
  background: #fce4ec;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-tip {
  text-align: center;
  padding: 30px 0;
}

/* 动画效果 */
.message-fade-enter-active,
.message-fade-leave-active {
  transition: all 0.8s ease;
}
.message-fade-enter-from {
  opacity: 0;
  transform: translateY(30px);
}
.message-fade-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}
</style>

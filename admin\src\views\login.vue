<template>
  <div class="love-container" :style="{ background: currentBg }">
    <!-- 浮动爱心粒子 -->
    <div class="floating-hearts">
      <div v-for="n in 15" :key="n" class="heart" :style="getHeartStyle(n)">
        ❤️
      </div>
    </div>

    <!-- 星空背景 -->
    <div class="stars-container">
      <div v-for="n in 50" :key="n" class="star" :style="getStarStyle(n)"></div>
    </div>

    <!-- 主标题动画 -->
    <div class="main-title">
      <h1 class="love-title">
        <span
          v-for="(char, index) in titleChars"
          :key="index"
          :style="{ animationDelay: index * 0.1 + 's' }"
          class="title-char"
        >
          {{ char }}
        </span>
      </h1>
      <div class="subtitle">{{ subtitle }}</div>
    </div>

    <!-- 3D情话卡片 -->
    <div class="love-card-3d" @click="flipCard">
      <div class="card-inner" :class="{ flipped: isFlipped }">
        <!-- 正面：情话展示 -->
        <div class="card-front">
          <div class="message-box">
            <!-- 装饰性元素 -->
            <div class="decorative-elements">
              <div class="rose-petals">
                <div
                  v-for="n in 8"
                  :key="n"
                  class="petal"
                  :style="getPetalStyle(n)"
                >
                  🌹
                </div>
              </div>
            </div>

            <transition name="message-fade" mode="out-in">
              <div :key="currentMessage.id" class="message-content">
                <p class="message-text">{{ currentMessage.text }}</p>
                <div class="message-category">
                  {{ currentMessage.category }}
                </div>
              </div>
            </transition>

            <div class="author-tag">
              <span class="heart-beat">💖</span>
              专属你的 {{ nickname }}
              <span class="heart-beat">💖</span>
            </div>

            <!-- 点击提示 -->
            <div class="click-hint">
              <span class="hint-text">点击卡片查看更多</span>
              <div class="pulse-ring"></div>
            </div>
          </div>
        </div>

        <!-- 背面：情话控制台 -->
        <div class="card-back">
          <div class="control-panel">
            <div class="panel-header">
              <h3>💕 情话宝库 💕</h3>
            </div>

            <el-tabs v-model="activeCategory" class="love-tabs">
              <el-tab-pane v-for="cat in categories" :key="cat" :label="cat">
                <div class="message-list">
                  <div
                    v-for="msg in filteredMessages"
                    :key="msg.id"
                    :class="{ active: msg.id === currentMessage.id }"
                    @click="selectMessage(msg)"
                    class="message-item"
                  >
                    <div class="message-preview">
                      {{ msg.text.substring(0, 30) }}...
                    </div>
                    <div class="message-meta">{{ msg.category }}</div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>

            <div class="action-row">
              <el-button
                type="primary"
                :icon="isFavorite ? 'Star' : 'StarFilled'"
                @click.stop="toggleFavorite"
                class="love-btn"
              >
                {{ isFavorite ? '取消收藏' : '收藏情话' }}
              </el-button>
              <el-button
                icon="Share"
                @click.stop="shareMessage"
                class="love-btn"
              >
                分享甜蜜
              </el-button>
              <el-button
                icon="View"
                @click.stop="showFavorites = true"
                class="love-btn"
              >
                收藏夹
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 互动功能区 -->
    <div class="interaction-area">
      <div class="action-btn-wrapper">
        <el-button
          circle
          icon="Refresh"
          @click="nextMessage"
          class="action-btn refresh-btn"
          title="换一句情话"
        />
        <span class="btn-label">换一句</span>
      </div>

      <div class="action-btn-wrapper">
        <el-button
          circle
          :icon="isPlaying ? 'VideoPause' : 'VideoPlay'"
          @click="toggleMusic"
          class="action-btn music-btn"
          :class="{ playing: isPlaying }"
          title="背景音乐"
        />
        <span class="btn-label">{{ isPlaying ? '暂停' : '音乐' }}</span>
      </div>

      <div class="action-btn-wrapper">
        <el-button
          circle
          icon="Picture"
          @click="changeBackground"
          class="action-btn bg-btn"
          title="切换背景"
        />
        <span class="btn-label">背景</span>
      </div>

      <div class="action-btn-wrapper">
        <el-button
          circle
          icon="MagicStick"
          @click="triggerSpecialEffect"
          class="action-btn magic-btn"
          title="特效"
        />
        <span class="btn-label">特效</span>
      </div>
    </div>

    <!-- 收藏弹窗 -->
    <el-dialog
      title="💖 你的情话收藏夹 💖"
      v-model="showFavorites"
      width="80%"
      class="favorites-dialog"
    >
      <div v-if="favoriteMessages.length > 0" class="favorites-content">
        <div
          v-for="msg in favoriteMessages"
          :key="msg.id"
          class="favorite-item"
        >
          <div class="favorite-text">{{ msg.text }}</div>
          <div class="favorite-meta">
            <span class="favorite-category">{{ msg.category }}</span>
            <el-button
              type="danger"
              icon="Delete"
              circle
              size="small"
              @click="removeFavorite(msg.id)"
              class="remove-btn"
            />
          </div>
        </div>
      </div>
      <div v-else class="empty-tip">
        <el-empty description="还没有收藏的情话哦~">
          <template #image>
            <div class="empty-heart">💔</div>
          </template>
        </el-empty>
      </div>
    </el-dialog>

    <!-- 特效层 -->
    <div class="special-effects" v-show="showSpecialEffect">
      <div class="fireworks">
        <div
          v-for="n in 20"
          :key="n"
          class="firework"
          :style="getFireworkStyle(n)"
        >
          ✨
        </div>
      </div>
    </div>

    <!-- 音频元素 -->
    <audio ref="bgMusic" loop>
      <source src="/romantic-music.mp3" type="audio/mpeg" />
    </audio>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';

// 用户数据
const nickname = ref('丹丹宝贝'); // 替换为对方昵称
const titleText = ref('💖 为你而来的浪漫 💖');
const subtitle = ref('每一句情话，都是我对你的心意');

// 标题字符分解
const titleChars = computed(() => titleText.value.split(''));

// 情话数据库（含分类标签）
const messages = ref([
  {
    id: 1,
    text: '你的眼睛是星星，每次眨眼都点亮我的宇宙',
    category: '浪漫',
    favorite: false,
  },
  {
    id: 2,
    text: '我想把余生写成诗，字字都是你的名字',
    category: '诗意',
    favorite: false,
  },
  {
    id: 3,
    text: '你是我的bug，让我日思夜想却舍不得修复',
    category: '程序员',
    favorite: false,
  },
  {
    id: 4,
    text: '如果爱情是一场游戏，那我愿意为你通关所有关卡',
    category: '游戏',
    favorite: false,
  },
  {
    id: 5,
    text: '你是我心中最美的代码，永远不会有bug',
    category: '程序员',
    favorite: false,
  },
  {
    id: 6,
    text: '春风十里不如你，夏日炎炎只想你',
    category: '诗意',
    favorite: false,
  },
  {
    id: 7,
    text: '你是我的小确幸，也是我的大梦想',
    category: '温馨',
    favorite: false,
  },
  {
    id: 8,
    text: '遇见你的那一刻，我的世界开始有了颜色',
    category: '浪漫',
    favorite: false,
  },
  {
    id: 9,
    text: '你的笑容是我见过最美的风景',
    category: '温馨',
    favorite: false,
  },
  {
    id: 10,
    text: '愿意陪你从青丝到白发，从心动到古稀',
    category: '承诺',
    favorite: false,
  },
]);

// 背景图库 - 使用渐变背景
const backgrounds = ref([
  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
]);

// 响应式数据
const currentMessage = ref(messages.value[0]);
const isFlipped = ref(false);
const activeCategory = ref('全部');
const isPlaying = ref(false);
const showFavorites = ref(false);
const currentBg = ref(backgrounds.value[0]);
const favoriteMessages = ref([]);
const showSpecialEffect = ref(false);
const bgMusic = ref(null);

// 分类系统
const categories = computed(() => {
  return ['全部', ...new Set(messages.value.map(m => m.category))];
});

// 过滤情话
const filteredMessages = computed(() => {
  return activeCategory.value === '全部'
    ? messages.value
    : messages.value.filter(m => m.category === activeCategory.value);
});

// 情话收藏功能
const isFavorite = computed(() => {
  return favoriteMessages.value.some(fav => fav.id === currentMessage.value.id);
});

const toggleFavorite = () => {
  if (isFavorite.value) {
    favoriteMessages.value = favoriteMessages.value.filter(
      fav => fav.id !== currentMessage.value.id
    );
    ElMessage.success('已移出收藏夹');
  } else {
    favoriteMessages.value.push({ ...currentMessage.value });
    ElMessage.success('❤️ 情话收藏成功！');
    triggerSpecialEffect(); // 收藏时触发特效
  }
};

const removeFavorite = id => {
  favoriteMessages.value = favoriteMessages.value.filter(fav => fav.id !== id);
  ElMessage.success('已从收藏夹移除');
};

// 情话互动方法
const nextMessage = () => {
  const currentIndex = messages.value.findIndex(
    m => m.id === currentMessage.value.id
  );
  const nextIndex = (currentIndex + 1) % messages.value.length;
  currentMessage.value = messages.value[nextIndex];
};

const selectMessage = msg => {
  currentMessage.value = msg;
  isFlipped.value = false; // 自动翻回正面
};

const flipCard = () => {
  isFlipped.value = !isFlipped.value;
};

const changeBackground = () => {
  const currentIndex = backgrounds.value.findIndex(
    bg => bg === currentBg.value
  );
  const nextIndex = (currentIndex + 1) % backgrounds.value.length;
  currentBg.value = backgrounds.value[nextIndex];
  ElMessage.success('🎨 背景已切换');
};

const shareMessage = () => {
  // 复制到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard.writeText(currentMessage.value.text).then(() => {
      ElMessage.success(
        `💕 情话已复制到剪贴板，快去分享给${nickname.value}吧！`
      );
    });
  } else {
    ElMessage.success(`💕 已将情话分享给${nickname.value}，请查收甜蜜！`);
  }
};

// 音乐控制
const toggleMusic = () => {
  if (!bgMusic.value) return;

  if (isPlaying.value) {
    bgMusic.value.pause();
    ElMessage.info('🎵 音乐已暂停');
  } else {
    bgMusic.value.play().catch(() => {
      ElMessage.warning('🎵 请先与页面交互后再播放音乐');
    });
    ElMessage.success('🎵 浪漫音乐开始播放');
  }
  isPlaying.value = !isPlaying.value;
};

// 特效控制
const triggerSpecialEffect = () => {
  showSpecialEffect.value = true;
  ElMessage.success('✨ 爱的魔法已释放！');

  setTimeout(() => {
    showSpecialEffect.value = false;
  }, 3000);
};

// 动态样式生成函数
const getHeartStyle = index => {
  const delay = Math.random() * 5;
  const duration = 3 + Math.random() * 2;
  const left = Math.random() * 100;

  return {
    left: `${left}%`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`,
    fontSize: `${0.8 + Math.random() * 0.4}rem`,
  };
};

const getStarStyle = index => {
  const top = Math.random() * 100;
  const left = Math.random() * 100;
  const delay = Math.random() * 3;
  const duration = 2 + Math.random() * 2;

  return {
    top: `${top}%`,
    left: `${left}%`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`,
  };
};

const getPetalStyle = index => {
  const angle = index * 45 + Math.random() * 20;
  const distance = 80 + Math.random() * 40;
  const delay = Math.random() * 2;

  return {
    transform: `rotate(${angle}deg) translateY(-${distance}px)`,
    animationDelay: `${delay}s`,
  };
};

const getFireworkStyle = index => {
  const top = Math.random() * 100;
  const left = Math.random() * 100;
  const delay = Math.random() * 1;

  return {
    top: `${top}%`,
    left: `${left}%`,
    animationDelay: `${delay}s`,
  };
};

// 初始化
onMounted(() => {
  // 设置背景为渐变
  currentBg.value = backgrounds.value[0];

  // 自动轮播情话
  setInterval(nextMessage, 15000);

  // 获取音频元素引用
  nextTick(() => {
    bgMusic.value = document.querySelector('audio');
  });
});
</script>

<style scoped>
/* 主容器 */
.love-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 背景动画 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 浮动爱心 */
.floating-hearts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.heart {
  position: absolute;
  animation: floatUp 5s linear infinite;
  opacity: 0.7;
}

@keyframes floatUp {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* 星空背景 */
.stars-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.star {
  position: absolute;
  width: 2px;
  height: 2px;
  background: white;
  border-radius: 50%;
  animation: twinkle 4s ease-in-out infinite;
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 主标题 */
.main-title {
  text-align: center;
  margin-bottom: 40px;
  z-index: 10;
  position: relative;
}

.love-title {
  font-size: 3rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title-char {
  display: inline-block;
  animation: bounceIn 1s ease-out both;
  color: #fff;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.1) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 10px;
  animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 3D卡片 */
.love-card-3d {
  perspective: 1500px;
  width: 90%;
  max-width: 650px;
  height: 450px;
  margin: 0 auto;
  z-index: 10;
  position: relative;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
}

.flipped {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.card-front {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.85) 100%
  );
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card-front:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.4);
}

.card-back {
  background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
  transform: rotateY(180deg);
  overflow: hidden;
}

/* 装饰元素 */
.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.rose-petals {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.petal {
  position: absolute;
  font-size: 1.5rem;
  animation: petalFloat 4s ease-in-out infinite;
  opacity: 0.6;
}

@keyframes petalFloat {
  0%,
  100% {
    transform: rotate(0deg) translateY(0px);
    opacity: 0.6;
  }
  50% {
    transform: rotate(180deg) translateY(-20px);
    opacity: 0.3;
  }
}

/* 消息内容 */
.message-box {
  text-align: center;
  padding: 40px;
  position: relative;
  z-index: 2;
}

.message-content {
  margin-bottom: 30px;
}

.message-text {
  font-size: 2.4rem;
  line-height: 1.6;
  color: #d81b60;
  font-weight: 600;
  margin-bottom: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  animation: textGlow 3s ease-in-out infinite;
}

@keyframes textGlow {
  0%,
  100% {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
  50% {
    text-shadow: 0 0 20px rgba(216, 27, 96, 0.3), 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.message-category {
  display: inline-block;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.author-tag {
  font-size: 1.4rem;
  color: #e91e63;
  font-style: italic;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.heart-beat {
  animation: heartBeat 1.5s ease-in-out infinite;
}

@keyframes heartBeat {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* 点击提示 */
.click-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.hint-text {
  color: rgba(216, 27, 96, 0.7);
  font-size: 0.9rem;
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.pulse-ring {
  width: 30px;
  height: 30px;
  border: 2px solid #e91e63;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* 控制面板 */
.control-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.panel-header {
  text-align: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
  color: #d81b60;
  font-size: 1.5rem;
}

.love-tabs {
  flex: 1;
}

.message-list {
  max-height: 250px;
  overflow-y: auto;
  padding: 10px 0;
}

.message-item {
  padding: 15px;
  margin: 8px 0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 4px solid transparent;
  background: rgba(248, 249, 255, 0.8);
}

.message-item:hover {
  background: linear-gradient(135deg, #fce4ec 0%, #f3e5f5 100%);
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.message-item.active {
  border-left-color: #e91e63;
  background: linear-gradient(135deg, #fce4ec 0%, #f3e5f5 100%);
  font-weight: bold;
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(233, 30, 99, 0.2);
}

.message-preview {
  font-size: 1rem;
  color: #333;
  margin-bottom: 5px;
}

.message-meta {
  font-size: 0.8rem;
  color: #666;
  opacity: 0.8;
}

/* 按钮样式 */
.action-row {
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.love-btn {
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
}

.love-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 互动区域 */
.interaction-area {
  margin-top: 50px;
  display: flex;
  gap: 30px;
  z-index: 10;
  position: relative;
}

.action-btn-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.action-btn {
  width: 70px;
  height: 70px;
  font-size: 1.8rem;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #d81b60;
}

.action-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #fff 0%, rgba(255, 255, 255, 0.9) 100%);
}

.refresh-btn:hover {
  animation: spin 0.5s ease-in-out;
}

@keyframes spin {
  from {
    transform: translateY(-5px) scale(1.05) rotate(0deg);
  }
  to {
    transform: translateY(-5px) scale(1.05) rotate(360deg);
  }
}

.music-btn.playing {
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  color: white;
  animation: musicPulse 1s ease-in-out infinite;
}

@keyframes musicPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.magic-btn:hover {
  animation: magicSparkle 0.6s ease-in-out;
}

@keyframes magicSparkle {
  0%,
  100% {
    transform: translateY(-5px) scale(1.05);
  }
  25% {
    transform: translateY(-5px) scale(1.1) rotate(5deg);
  }
  75% {
    transform: translateY(-5px) scale(1.1) rotate(-5deg);
  }
}

.btn-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 收藏夹样式 */
.favorites-dialog {
  border-radius: 20px;
}

.favorites-content {
  max-height: 400px;
  overflow-y: auto;
}

.favorite-item {
  padding: 20px;
  margin: 15px 0;
  background: linear-gradient(135deg, #fce4ec 0%, #f3e5f5 100%);
  border-radius: 15px;
  border-left: 5px solid #e91e63;
  transition: all 0.3s ease;
  position: relative;
}

.favorite-item:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 20px rgba(233, 30, 99, 0.2);
}

.favorite-text {
  font-size: 1.1rem;
  color: #333;
  line-height: 1.6;
  margin-bottom: 10px;
}

.favorite-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.favorite-category {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
  padding: 3px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.remove-btn {
  transition: all 0.3s ease;
}

.remove-btn:hover {
  transform: scale(1.1);
}

.empty-tip {
  text-align: center;
  padding: 50px 0;
}

.empty-heart {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: brokenHeart 2s ease-in-out infinite;
}

@keyframes brokenHeart {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 特效层 */
.special-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.fireworks {
  position: relative;
  width: 100%;
  height: 100%;
}

.firework {
  position: absolute;
  font-size: 2rem;
  animation: fireworkExplode 3s ease-out forwards;
}

@keyframes fireworkExplode {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.5) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(2) rotate(360deg);
    opacity: 0;
  }
}

/* 消息过渡动画 */
.message-fade-enter-active,
.message-fade-leave-active {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-fade-enter-from {
  opacity: 0;
  transform: translateY(50px) scale(0.8);
}

.message-fade-leave-to {
  opacity: 0;
  transform: translateY(-50px) scale(0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .love-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .love-card-3d {
    width: 95%;
    height: 380px;
  }

  .message-text {
    font-size: 1.8rem;
  }

  .interaction-area {
    gap: 20px;
  }

  .action-btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .btn-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    margin-bottom: 30px;
  }

  .love-title {
    font-size: 1.5rem;
  }

  .love-card-3d {
    height: 320px;
  }

  .message-text {
    font-size: 1.5rem;
  }

  .interaction-area {
    flex-wrap: wrap;
    gap: 15px;
  }

  .action-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}
</style>
